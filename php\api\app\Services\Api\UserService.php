<?php

namespace App\Services\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\User;
use App\Models\UserPreference;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 用户管理服务
 */
class UserService
{
    /**
     * 更新用户偏好设置
     */
    public function updatePreferences(int $userId, array $preferences): array
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);
            
            // 获取或创建用户偏好设置
            $userPreference = $user->preference;
            if (!$userPreference) {
                $userPreference = UserPreference::create(['user_id' => $userId]);
            }

            // 更新偏好设置
            $userPreference->fill($preferences);
            $userPreference->save();

            DB::commit();

            Log::info('用户偏好设置更新成功', [
                'user_id' => $userId,
                'updated_fields' => array_keys($preferences)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '偏好设置更新成功',
                'data' => [
                    'language' => $userPreference->language,
                    'timezone' => $userPreference->timezone,
                    'email_notifications' => $userPreference->email_notifications,
                    'push_notifications' => $userPreference->push_notifications,
                    'ai_preferences' => $userPreference->ai_preferences ?? [],
                    'ui_preferences' => $userPreference->ui_preferences ?? [],
                    'workflow_preferences' => $userPreference->workflow_preferences ?? [],
                    'default_ai_model' => $userPreference->default_ai_model,
                    'auto_save_interval' => $userPreference->auto_save_interval,
                    'show_tutorials' => $userPreference->show_tutorials
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            $error_context = [
                'user_id' => $userId,
                'preferences_keys' => array_keys($preferences),
            ];

            Log::error('用户偏好设置更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户偏好设置更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户统计信息
     */
    public function getUserStats(int $userId): array
    {
        try {
            $user = User::with(['pointsTransactions', 'projects'])->findOrFail($userId);
            
            // 计算统计数据
            $totalTransactions = $user->pointsTransactions->count();
            $totalProjects = $user->projects->count();
            $completedProjects = $user->projects->where('status', 'completed')->count();
            $publishedProjects = $user->projects->where('status', 'published')->count();
            
            // 计算积分统计
            $totalRecharge = $user->pointsTransactions
                ->where('business_type', 'recharge')
                ->where('status', 'success')
                ->sum('amount');
                
            $totalConsumption = $user->pointsTransactions
                ->whereIn('business_type', ['text_to_image', 'image_to_video', 'text_generation', 'voice_synthesis'])
                ->where('status', 'success')
                ->sum('amount');

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'user_info' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'nickname' => $user->nickname,
                        'is_vip' => $user->is_vip,
                        'created_at' => $user->created_at->format('Y-m-d H:i:s')
                    ],
                    'points_stats' => [
                        'current_points' => $user->points,
                        'frozen_points' => $user->frozen_points,
                        'total_recharge' => $totalRecharge,
                        'total_consumption' => $totalConsumption
                    ],
                    'project_stats' => [
                        'total_projects' => $totalProjects,
                        'completed_projects' => $completedProjects,
                        'published_projects' => $publishedProjects,
                        'completion_rate' => $totalProjects > 0 ? round($completedProjects / $totalProjects * 100, 2) : 0
                    ],
                    'activity_stats' => [
                        'total_transactions' => $totalTransactions,
                        'last_login_at' => $user->last_login_at?->format('Y-m-d H:i:s'),
                        'account_age_days' => $user->created_at->diffInDays(Carbon::now())
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
            ];

            Log::error('获取用户统计信息失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取用户统计信息失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新用户基本信息
     */
    public function updateProfile(int $userId, array $profileData): array
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);
            
            // 只允许更新特定字段
            $allowedFields = ['nickname', 'email', 'avatar'];
            $updateData = array_intersect_key($profileData, array_flip($allowedFields));
            
            if (empty($updateData)) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '没有可更新的字段',
                    'data' => []
                ];
            }

            // 检查邮箱唯一性
            if (isset($updateData['email']) && $updateData['email'] !== $user->email) {
                $existingUser = User::where('email', $updateData['email'])->where('id', '!=', $userId)->first();
                if ($existingUser) {
                    return [
                        'code' => ApiCodeEnum::EMAIL_ALREADY_EXISTS,
                        'message' => '邮箱已被使用',
                        'data' => []
                    ];
                }
            }

            $user->fill($updateData);
            $user->save();

            DB::commit();

            Log::info('用户基本信息更新成功', [
                'user_id' => $userId,
                'updated_fields' => array_keys($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '个人信息更新成功',
                'data' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'nickname' => $user->nickname,
                    'email' => $user->email,
                    'avatar' => $user->avatar
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            $error_context = [
                'user_id' => $userId,
                'profile_data_keys' => array_keys($profileData),
            ];

            Log::error('用户基本信息更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户基本信息更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 重置用户偏好设置为默认值
     */
    public function resetPreferences(int $userId): array
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);
            $userPreference = $user->preference;
            
            if (!$userPreference) {
                $userPreference = UserPreference::create(['user_id' => $userId]);
            } else {
                $userPreference->resetToDefaults();
                $userPreference->save();
            }

            DB::commit();

            Log::info('用户偏好设置重置成功', ['user_id' => $userId]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '偏好设置已重置为默认值',
                'data' => [
                    'language' => $userPreference->language,
                    'timezone' => $userPreference->timezone,
                    'email_notifications' => $userPreference->email_notifications,
                    'push_notifications' => $userPreference->push_notifications,
                    'auto_save_interval' => $userPreference->auto_save_interval,
                    'show_tutorials' => $userPreference->show_tutorials
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
            ];

            Log::error('用户偏好设置重置失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户偏好设置重置失败',
                'data' => null
            ];
        }
    }
}
