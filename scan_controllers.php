<?php

/**
 * 扫描控制器文件，检查public function方法中是否包含error_context字符串
 * 使用更精确的方法来解析PHP代码
 */

$controllerDir = 'php/api/app/Http/Controllers/Api';
$results = [];
$totalMethods = 0;
$methodsWithErrorContext = 0;
$methodsWithoutErrorContext = 0;

// 获取所有控制器文件
$files = glob($controllerDir . '/*.php');

echo "开始扫描 " . count($files) . " 个控制器文件...\n\n";

foreach ($files as $file) {
    $filename = basename($file);
    echo "正在扫描: $filename\n";

    $content = file_get_contents($file);
    if ($content === false) {
        echo "  错误: 无法读取文件 $filename\n";
        continue;
    }

    // 移除注释，避免干扰
    $content = preg_replace('/\/\*.*?\*\//s', '', $content);
    $content = preg_replace('/\/\/.*$/m', '', $content);

    // 更精确的正则表达式来匹配public function
    // 匹配整个方法体，包括嵌套的大括号
    preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*(\{(?:[^{}]|(?2))*\})/s', $content, $matches, PREG_SET_ORDER);

    $methodsWithoutErrorContextInFile = [];
    $methodsWithErrorContextInFile = [];

    foreach ($matches as $match) {
        $methodName = $match[1];
        $methodBody = $match[2];

        // 跳过构造函数
        if ($methodName === '__construct') {
            continue;
        }

        $totalMethods++;

        // 检查方法体中是否包含error_context
        if (strpos($methodBody, 'error_context') !== false) {
            $methodsWithErrorContext++;
            $methodsWithErrorContextInFile[] = $methodName;
        } else {
            $methodsWithoutErrorContext++;
            $methodsWithoutErrorContextInFile[] = $methodName;
        }
    }

    echo "  - 总方法数: " . count($matches) . "\n";
    echo "  - 包含error_context: " . count($methodsWithErrorContextInFile) . "\n";
    echo "  - 不包含error_context: " . count($methodsWithoutErrorContextInFile) . "\n";

    if (!empty($methodsWithoutErrorContextInFile)) {
        $results[] = [
            'file' => $filename,
            'methods' => $methodsWithoutErrorContextInFile,
            'with_error_context' => $methodsWithErrorContextInFile
        ];
    }
}

// 输出统计信息
echo "\n" . str_repeat("=", 80) . "\n";
echo "扫描统计信息\n";
echo str_repeat("=", 80) . "\n";
echo "总控制器文件数: " . count($files) . "\n";
echo "总public function方法数: $totalMethods\n";
echo "包含error_context的方法数: $methodsWithErrorContext\n";
echo "不包含error_context的方法数: $methodsWithoutErrorContext\n";
echo "\n";

// 输出结果
echo str_repeat("=", 80) . "\n";
echo "扫描结果报告 - 不包含error_context的方法\n";
echo str_repeat("=", 80) . "\n\n";

if (empty($results)) {
    echo "所有控制器的public function方法都包含error_context字符串。\n";
} else {
    $counter = 1;
    foreach ($results as $result) {
        $methodCount = count($result['methods']);
        echo "### {$counter}. {$result['file']} ({$methodCount}个方法)\n";

        foreach ($result['methods'] as $method) {
            echo "- [ ] `{$method}()`\n";
        }
        echo "\n";
        $counter++;
    }
}

echo "扫描完成！\n";
