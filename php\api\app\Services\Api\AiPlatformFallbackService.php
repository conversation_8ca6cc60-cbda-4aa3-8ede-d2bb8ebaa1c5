<?php

namespace App\Services\Api;

use App\Helpers\LogCheckHelper;
use App\Models\AiModelConfig;
use App\Models\UserModelPreference;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class AiPlatformFallbackService
{
    protected AiPlatformSelectionService $selectionService;
    protected AiPlatformHealthService $healthService;

    public function __construct(
        AiPlatformSelectionService $selectionService,
        AiPlatformHealthService $healthService
    ) {
        $this->selectionService = $selectionService;
        $this->healthService = $healthService;
    }

    /**
     * 执行平台降级
     */
    public function executeFallback(
        string $originalPlatform,
        string $businessType,
        int $userId,
        array $taskConfig = []
    ): array {
        try {
            Log::info('开始执行AI平台降级', [
                'original_platform' => $originalPlatform,
                'business_type' => $businessType,
                'user_id' => $userId,
                'task_config' => $taskConfig
            ]);

            // 🔧 新增：验证原平台是否支持该业务类型
            if (!$this->selectionService->isPlatformSupported($originalPlatform, $businessType)) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => "平台 {$originalPlatform} 不支持 {$businessType} 业务类型",
                    'data' => [
                        'original_platform' => $originalPlatform,
                        'business_type' => $businessType,
                        'supported_platforms' => $this->selectionService->getSupportedPlatforms($businessType)
                    ]
                ];
            }

            // 检查原平台状态
            $originalStatus = $this->healthService->checkPlatformHealth($originalPlatform);
            
            // 获取用户降级偏好
            $userPreference = $this->getUserFallbackPreference($userId, $businessType);
            
            if (!$userPreference || !$userPreference->auto_fallback) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '用户未启用自动降级功能',
                    'data' => [
                        'original_platform' => $originalPlatform,
                        'original_status' => $originalStatus,
                        'fallback_disabled' => true,
                        'suggestion' => '请手动选择其他平台或启用自动降级功能'
                    ]
                ];
            }

            // 获取备选平台（排除原平台）
            $fallbackCriteria = array_merge($taskConfig, [
                'exclude_platforms' => [$originalPlatform],
                'fallback_mode' => true
            ]);

            $fallbackResult = $this->selectionService->selectOptimalPlatform(
                $businessType,
                $userId,
                $fallbackCriteria
            );

            if ($fallbackResult['code'] !== ApiCodeEnum::SUCCESS) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '没有可用的备选平台',
                    'data' => [
                        'original_platform' => $originalPlatform,
                        'original_status' => $originalStatus,
                        'business_type' => $businessType,
                        'supported_platforms' => $this->selectionService->getSupportedPlatforms($businessType),
                        'fallback_error' => $fallbackResult['message']
                    ]
                ];
            }

            $fallbackPlatform = $fallbackResult['data']['selected_platform'];

            // 🔧 新增：验证降级平台的可用性
            $fallbackHealth = $this->healthService->checkPlatformHealth($fallbackPlatform['platform']);
            if ($fallbackHealth['status'] !== 'healthy') {
                Log::warning('降级平台健康状态异常', [
                    'fallback_platform' => $fallbackPlatform['platform'],
                    'health_status' => $fallbackHealth['status'],
                    'original_platform' => $originalPlatform
                ]);
            }

            // 记录降级事件
            $this->recordFallbackEvent($originalPlatform, $fallbackPlatform, $businessType, $userId, $originalStatus);

            // 🔧 新增：更新用户偏好（学习用户行为）
            $this->updateUserPreferenceFromFallback($userId, $businessType, $originalPlatform, $fallbackPlatform['platform']);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'AI平台降级成功',
                'data' => [
                    'original_platform' => $originalPlatform,
                    'original_status' => $originalStatus,
                    'fallback_platform' => $fallbackPlatform,
                    'fallback_health' => $fallbackHealth,
                    'fallback_reason' => $this->generateFallbackReason($originalStatus),
                    'estimated_recovery_time' => $this->estimateRecoveryTime($originalPlatform),
                    'cost_impact' => $this->calculateCostImpact($originalPlatform, $fallbackPlatform['platform']),
                    'fallback_time' => now()->toISOString()
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'original_platform' => $originalPlatform,
                'business_type' => $businessType,
                'user_id' => $userId,
                'task_config' => $taskConfig,
            ];

            Log::error('AI平台降级失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'AI平台降级失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户降级偏好
     */
    protected function getUserFallbackPreference(int $userId, string $businessType): ?UserModelPreference
    {
        return UserModelPreference::where('user_id', $userId)
            ->where('business_type', $businessType)
            ->first();
    }

    /**
     * 生成降级原因
     */
    protected function generateFallbackReason(array $originalStatus): string
    {
        switch ($originalStatus['status']) {
            case 'unhealthy':
                return '原平台服务异常，自动切换到备选平台';
            case 'unknown':
                return '原平台状态未知，为确保服务稳定性自动切换';
            default:
                if (isset($originalStatus['response_time']) && $originalStatus['response_time'] > 5000) {
                    return '原平台响应时间过长，自动切换到更快的备选平台';
                }
                return '根据当前负载情况自动切换到最优平台';
        }
    }

    /**
     * 估算恢复时间
     */
    protected function estimateRecoveryTime(string $platform): ?string
    {
        // 🔧 修正：优化缓存键命名
        $cacheKey = "ai_platform_recovery_estimate_{$platform}";
        
        return Cache::remember($cacheKey, 300, function () use ($platform) {
            // 🔧 修正：基于平台特性和历史数据估算恢复时间
            $recoveryTimes = [
                'deepseek' => 30,    // 30分钟
                'liblib' => 60,     // 1小时
                'kling' => 45,      // 45分钟
                'minimax' => 20,    // 20分钟
                'volcengine' => 40  // 40分钟
            ];

            $baseMinutes = $recoveryTimes[$platform] ?? 60;
            
            // 🔧 新增：根据当前时间调整（工作时间恢复更快）
            $currentHour = now()->hour;
            if ($currentHour >= 9 && $currentHour <= 18) {
                $baseMinutes = (int)($baseMinutes * 0.7); // 工作时间减少30%
            }
            
            return now()->addMinutes($baseMinutes)->toISOString();
        });
    }

    /**
     * 记录降级事件
     */
    protected function recordFallbackEvent(
        string $originalPlatform,
        array $fallbackPlatform,
        string $businessType,
        int $userId,
        array $originalStatus
    ): void {
        // 🔧 修正：记录更详细的降级信息
        Log::info('AI平台降级事件记录', [
            'event_type' => 'platform_fallback',
            'user_id' => $userId,
            'business_type' => $businessType,
            'original_platform' => $originalPlatform,
            'original_status' => $originalStatus['status'],
            'original_response_time' => $originalStatus['response_time'] ?? 0,
            'fallback_platform' => $fallbackPlatform['platform'],
            'fallback_score' => $fallbackPlatform['score'],
            'fallback_priority' => $fallbackPlatform['priority'],
            'fallback_reason' => $fallbackPlatform['selection_reason'] ?? 'auto_selected',
            'timestamp' => now()->toISOString()
        ]);

        // 🔧 新增：更新降级统计
        $this->updateFallbackStatistics($originalPlatform, $fallbackPlatform['platform'], $businessType);
    }

    /**
     * 🔧 新增：更新降级统计
     */
    protected function updateFallbackStatistics(string $originalPlatform, string $fallbackPlatform, string $businessType): void
    {
        $statsKey = "ai_platform_fallback_stats_{$businessType}";
        $stats = Cache::get($statsKey, []);
        
        $fallbackKey = "{$originalPlatform}_to_{$fallbackPlatform}";
        $stats[$fallbackKey] = ($stats[$fallbackKey] ?? 0) + 1;
        
        Cache::put($statsKey, $stats, 86400); // 24小时缓存
    }

    /**
     * 🔧 新增：计算成本影响
     */
    protected function calculateCostImpact(string $originalPlatform, string $fallbackPlatform): array
    {
        try {
            $originalModel = AiModelConfig::where('platform', $originalPlatform)
                ->where('is_active', true)
                ->first();
            
            $fallbackModel = AiModelConfig::where('platform', $fallbackPlatform)
                ->where('is_active', true)
                ->first();
            
            if (!$originalModel || !$fallbackModel) {
                return [
                    'impact' => 'unknown',
                    'message' => '无法计算成本影响'
                ];
            }
            
            $costDiff = $fallbackModel->cost_per_request - $originalModel->cost_per_request;
            $percentageChange = $originalModel->cost_per_request > 0 
                ? ($costDiff / $originalModel->cost_per_request) * 100 
                : 0;
            
            return [
                'original_cost' => $originalModel->cost_per_request,
                'fallback_cost' => $fallbackModel->cost_per_request,
                'cost_difference' => $costDiff,
                'percentage_change' => round($percentageChange, 2),
                'impact' => $costDiff > 0 ? 'increase' : ($costDiff < 0 ? 'decrease' : 'no_change'),
                'message' => $this->generateCostImpactMessage($costDiff, $percentageChange)
            ];
            
        } catch (\Exception $e) {
            Log::error('计算降级成本影响失败', [
                'original_platform' => $originalPlatform,
                'fallback_platform' => $fallbackPlatform,
                'error' => $e->getMessage()
            ]);
            
            return [
                'impact' => 'unknown',
                'message' => '计算成本影响时发生错误'
            ];
        }
    }

    /**
     * 🔧 新增：生成成本影响消息
     */
    protected function generateCostImpactMessage(float $costDiff, float $percentageChange): string
    {
        if (abs($costDiff) < 0.001) {
            return '成本基本无变化';
        } elseif ($costDiff > 0) {
            return "成本增加 {$percentageChange}%";
        } else {
            return "成本降低 " . abs($percentageChange) . "%";
        }
    }

    /**
     * 🔧 新增：从降级中学习，更新用户偏好
     */
    protected function updateUserPreferenceFromFallback(
        int $userId, 
        string $businessType, 
        string $originalPlatform, 
        string $fallbackPlatform
    ): void {
        try {
            $preference = UserModelPreference::firstOrCreate([
                'user_id' => $userId,
                'business_type' => $businessType
            ], [
                'preferred_platform' => $fallbackPlatform,
                'platform_priorities' => [],
                'selection_criteria' => ['reliability' => 0.8, 'cost' => 0.2],
                'auto_fallback' => true,
                'cost_optimization' => false
            ]);

            // 降低原平台优先级
            $priorities = $preference->platform_priorities;
            $priorities[$originalPlatform] = max(0, ($priorities[$originalPlatform] ?? 50) - 10);
            
            // 提高降级平台优先级
            $priorities[$fallbackPlatform] = min(100, ($priorities[$fallbackPlatform] ?? 50) + 5);
            
            $preference->update([
                'platform_priorities' => $priorities,
                'last_used_at' => now()
            ]);

            Log::info('从降级中学习，更新用户偏好', [
                'user_id' => $userId,
                'business_type' => $businessType,
                'original_platform' => $originalPlatform,
                'fallback_platform' => $fallbackPlatform,
                'updated_priorities' => $priorities
            ]);

        } catch (\Exception $e) {
            Log::error('更新用户偏好失败', [
                'user_id' => $userId,
                'business_type' => $businessType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查平台是否需要降级
     */
    public function shouldFallback(string $platform, string $businessType): bool
    {
        try {
            // 原服务层的业务代码逻辑
            $healthStatus = $this->healthService->checkPlatformHealth($platform);

            // 降级条件
            if ($healthStatus['status'] === 'unhealthy') {
                return true;
            }

            if ($healthStatus['response_time'] > 10000) { // 响应时间超过10秒
                return true;
            }

            // 检查最近的成功率
            $availability = $this->healthService->getPlatformAvailability($platform, 1);
            if ($availability['success_rate'] < 80) { // 成功率低于80%
                return true;
            }

            return false;

        } catch (\Exception $e) {
            $error_context = [
                'platform' => $platform,
                'business_type' => $businessType,
            ];

            Log::error('检查平台降级状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 发生异常时默认返回需要降级
            return true;
        }
    }
}
