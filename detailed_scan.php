<?php

/**
 * 详细扫描单个控制器文件，逐个方法检查error_context
 */

function extractMethodBody($content, $methodName) {
    // 找到方法开始位置
    $pattern = '/public\s+function\s+' . preg_quote($methodName) . '\s*\([^)]*\)\s*\{/';
    if (!preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
        return null;
    }
    
    $start = $matches[0][1] + strlen($matches[0][0]);
    $braceCount = 1;
    $pos = $start;
    
    while ($pos < strlen($content) && $braceCount > 0) {
        if ($content[$pos] === '{') {
            $braceCount++;
        } elseif ($content[$pos] === '}') {
            $braceCount--;
        }
        $pos++;
    }
    
    return substr($content, $start, $pos - $start - 1);
}

function scanController($filename) {
    $file = "php/api/app/Http/Controllers/Api/$filename";
    echo "=== 详细扫描: $filename ===\n";
    
    $content = file_get_contents($file);
    if ($content === false) {
        echo "错误: 无法读取文件\n";
        return;
    }
    
    // 移除注释
    $content = preg_replace('/\/\*.*?\*\//s', '', $content);
    $content = preg_replace('/\/\/.*$/m', '', $content);
    
    // 匹配所有public function
    preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)/', $content, $matches);
    
    $methodsWithoutErrorContext = [];
    $methodsWithErrorContext = [];
    
    foreach ($matches[1] as $methodName) {
        if ($methodName === '__construct') {
            continue;
        }
        
        $methodBody = extractMethodBody($content, $methodName);
        if ($methodBody === null) {
            echo "警告: 无法提取方法 $methodName 的方法体\n";
            continue;
        }
        
        $hasErrorContext = strpos($methodBody, 'error_context') !== false;
        
        echo "方法: $methodName() - " . ($hasErrorContext ? "包含" : "不包含") . " error_context\n";
        
        if ($hasErrorContext) {
            $methodsWithErrorContext[] = $methodName;
        } else {
            $methodsWithoutErrorContext[] = $methodName;
        }
    }
    
    echo "总计: " . count($matches[1]) . " 个方法\n";
    echo "包含error_context: " . count($methodsWithErrorContext) . " 个\n";
    echo "不包含error_context: " . count($methodsWithoutErrorContext) . " 个\n";
    
    if (!empty($methodsWithoutErrorContext)) {
        echo "不包含error_context的方法:\n";
        foreach ($methodsWithoutErrorContext as $method) {
            echo "  - $method()\n";
        }
    }
    echo "\n";
}

// 扫描指定的控制器
$controllers = [
    'AiModelController.php',
    'AdController.php',
    'AssetController.php'
];

foreach ($controllers as $controller) {
    scanController($controller);
}
