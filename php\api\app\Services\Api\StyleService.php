<?php

namespace App\Services\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\StyleLibrary;
use Illuminate\Support\Facades\Log;

/**
 * 剧情风格管理服务
 */
class StyleService
{
    /**
     * 创建新风格
     */
    public function createStyle(array $styleData): array
    {
        try {
            $style = StyleLibrary::create($styleData);

            Log::info('剧情风格创建成功', [
                'style_id' => $style->id,
                'name' => $style->name,
                'category' => $style->category
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '风格创建成功',
                'data' => [
                    'id' => $style->id,
                    'name' => $style->name,
                    'category' => $style->category
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'name' => $styleData['name'] ?? null,
                'category' => $styleData['category'] ?? null,
                'description' => $styleData['description'] ?? null,
            ];

            Log::error('剧情风格创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '风格创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新风格评分
     */
    public function updateRating(int $styleId, float $rating): array
    {
        try {
            $style = StyleLibrary::findOrFail($styleId);
            $style->updateRating($rating);

            Log::info('风格评分更新成功', [
                'style_id' => $styleId,
                'old_rating' => $style->rating,
                'new_rating' => $rating
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '评分更新成功',
                'data' => [
                    'style_id' => $styleId,
                    'new_rating' => $rating
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'style_id' => $styleId,
                'rating' => $rating,
            ];

            Log::error('风格评分更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '评分更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取推荐风格
     */
    public function getRecommendedStyles(int $userId, int $limit = 10): array
    {
        try {
            // 这里可以根据用户的历史使用记录进行个性化推荐
            // 目前先返回热门风格
            $styles = StyleLibrary::active()
                ->popular($limit)
                ->get(['id', 'name', 'description', 'category', 'thumbnail', 'rating', 'usage_count']);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $styles->toArray()
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'limit' => $limit,
            ];

            Log::error('获取推荐风格失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取推荐风格失败',
                'data' => null
            ];
        }
    }

    /**
     * 搜索风格
     */
    public function searchStyles(string $keyword, array $filters = []): array
    {
        try {
            $query = StyleLibrary::active();

            // 关键词搜索
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%")
                      ->orWhereJsonContains('tags', $keyword);
                });
            }

            // 分类筛选
            if (!empty($filters['category'])) {
                $query->byCategory($filters['category']);
            }

            // 高级风格筛选
            if (isset($filters['is_premium'])) {
                if ($filters['is_premium']) {
                    $query->premium();
                } else {
                    $query->free();
                }
            }

            // 评分筛选
            if (!empty($filters['min_rating'])) {
                $query->where('rating', '>=', $filters['min_rating']);
            }

            $styles = $query->ordered()
                ->get(['id', 'name', 'description', 'category', 'thumbnail', 'is_premium', 'rating', 'usage_count', 'tags']);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'keyword' => $keyword,
                    'total' => $styles->count(),
                    'styles' => $styles->toArray()
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'keyword' => $keyword,
                'filters' => $filters,
            ];

            Log::error('搜索风格失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '搜索失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取风格分类统计
     */
    public function getCategoryStats(): array
    {
        try {
            $stats = StyleLibrary::active()
                ->selectRaw('category, COUNT(*) as count, AVG(rating) as avg_rating, SUM(usage_count) as total_usage')
                ->groupBy('category')
                ->orderBy('total_usage', 'desc')
                ->get();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $stats->map(function($stat) {
                    return [
                        'category' => $stat->category,
                        'count' => $stat->count,
                        'avg_rating' => round($stat->avg_rating, 2),
                        'total_usage' => $stat->total_usage
                    ];
                })->toArray()
            ];

        } catch (\Exception $e) {
            $error_context = [];

            Log::error('获取风格分类统计失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取统计信息失败',
                'data' => null
            ];
        }
    }
}
