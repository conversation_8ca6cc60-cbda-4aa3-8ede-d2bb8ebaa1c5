<?php

namespace App\Services\Api;

use App\Helpers\LogCheckHelper;
use App\Models\WorkPlaza;
use App\Models\UserWork;
use App\Models\WorkShare;
use App\Models\WorkInteraction;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * 🎯 作品发布服务
 * 严格按照dev-api-guidelines-add.mdc规范实现
 * 对应规范第14061行：作品发布服务
 */
class WorkPublishService
{
    protected $permissionService;

    public function __construct(WorkPublishPermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * 🔧 LongDev1标记：发布作品 - 按规范第14065行
     * 
     * @param array $requestData 发布请求数据
     * @param int $userId 用户ID
     * @return array
     */
    public function publishWork(array $requestData, int $userId): array
    {
        try {
            DB::beginTransaction();

            // 🔧 LongDev1标记：1. 检查发布权限 - 按规范第14067行
            $permission = $this->permissionService->checkPublishPermission(
                $requestData['module_type'],
                $requestData['module_id'],
                $userId
            );

            if (!$permission['allowed']) {
                return [
                    'code' => ApiCodeEnum::PERMISSION_DENIED,
                    'message' => $permission['reason'],
                    'data' => $permission
                ];
            }

            // 🔧 LongDev1标记：2. 上传文件到作品广场存储 - 按规范第14079行
            $uploadResult = $this->uploadWorkFile($requestData['file']);

            // 🔧 LongDev1标记：3. 创建作品广场记录 - 按规范第14082行
            $work = WorkPlaza::create([
                'work_uuid' => Str::uuid(),
                'user_id' => $userId,
                'work_type' => $this->permissionService->mapModuleTypeToWorkType($requestData['module_type']),
                'file_name' => $uploadResult['file_name'],
                'file_extension' => $uploadResult['file_extension'],
                'file_size' => $uploadResult['file_size'],
                'mime_type' => $uploadResult['mime_type'],
                'file_path' => $uploadResult['file_path'],
                'work_title' => $requestData['title'],
                'work_description' => $requestData['description'] ?? null,
                'work_tags' => $requestData['tags'] ?? [],
                'source_resource_id' => $requestData['resource_id'] ?? null,
                'source_module_id' => $requestData['module_id'],
                'status' => WorkPlaza::STATUS_AUTO_REVIEW, // 机审
                'auto_review_result' => WorkPlaza::AUTO_REVIEW_APPROVED, // 按规范第14097行
                'auto_review_reason' => '基于资源版本审核状态：' . $permission['code'],
                'publish_status' => WorkPlaza::PUBLISH_STATUS_PUBLISHED,
                'published_at' => now()
            ]);

            // 🔧 LongDev1标记：4. 记录发布日志 - 按规范第14103行
            $this->logWorkPublish($work, $permission);

            DB::commit();

            Log::info('作品发布成功', [
                'work_id' => $work->id,
                'work_uuid' => $work->work_uuid,
                'user_id' => $userId,
                'module_type' => $requestData['module_type'],
                'module_id' => $requestData['module_id']
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '作品发布成功',
                'data' => [
                    'work_id' => $work->id,
                    'work_uuid' => $work->work_uuid,
                    'title' => $work->work_title,
                    'work_type' => $work->work_type,
                    'publish_status' => $work->publish_status,
                    'published_at' => $work->published_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'module_type' => $requestData['module_type'] ?? null,
                'module_id' => $requestData['module_id'] ?? null,
                'title' => $requestData['title'] ?? null,
                'work_type' => $requestData['work_type'] ?? null,
            ];

            Log::error('作品发布失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '作品发布失败',
                'data' => null
            ];
        }
    }

    /**
     * 🔧 LongDev1标记：上传作品文件 - 按规范第14079行
     * 
     * @param \Illuminate\Http\UploadedFile $file
     * @return array
     */
    private function uploadWorkFile($file): array
    {
        $originalName = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $mimeType = $file->getMimeType();
        $fileSize = $file->getSize();

        // 🔧 LongDev1标记：重命名文件 - 按规范第3068行
        $newFileName = 'work_' . time() . '_' . Str::random(16) . '.' . $extension;

        // 🔧 LongDev1标记：存储到专门的发布资源目录 - 按规范第3070行
        $storagePath = 'publish_resources/works/' . date('Y/m/d');
        $fullPath = $file->storeAs($storagePath, $newFileName, 'public');

        return [
            'file_name' => $originalName,
            'file_extension' => $extension,
            'file_size' => $fileSize,
            'mime_type' => $mimeType,
            'file_path' => $fullPath
        ];
    }

    /**
     * 🔧 LongDev1标记：记录发布日志 - 按规范第14103行
     * 
     * @param WorkPlaza $work
     * @param array $permission
     */
    private function logWorkPublish(WorkPlaza $work, array $permission): void
    {
        Log::info('作品发布权限检查通过', [
            'work_id' => $work->id,
            'permission_code' => $permission['code'],
            'permission_reason' => $permission['reason'],
            'resources_count' => $permission['resources'] ?? 0
        ]);
    }

    /**
     * 🔧 LongDev1标记：获取我的作品列表
     * 
     * @param int $userId
     * @param array $filters
     * @return array
     */
    public function getMyWorks(int $userId, array $filters = []): array
    {
        try {
            $query = WorkPlaza::where('user_id', $userId);

            // 应用筛选条件
            if (isset($filters['work_type'])) {
                $query->where('work_type', $filters['work_type']);
            }

            if (isset($filters['publish_status'])) {
                $query->where('publish_status', $filters['publish_status']);
            }

            $works = $query->orderBy('created_at', 'desc')
                          ->paginate($filters['per_page'] ?? 20);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取成功',
                'data' => [
                    'works' => $works->items(),
                    'pagination' => [
                        'current_page' => $works->currentPage(),
                        'per_page' => $works->perPage(),
                        'total' => $works->total(),
                        'last_page' => $works->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters
            ];

            Log::error('获取我的作品失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取失败',
                'data' => []
            ];
        }
    }

    /**
     * 🔧 LongDev1标记：获取作品展示库
     * 
     * @param array $filters
     * @return array
     */
    public function getGallery(array $filters = []): array
    {
        try {
            $query = WorkPlaza::published();

            // 应用筛选条件
            if (isset($filters['work_type'])) {
                $query->ofType($filters['work_type']);
            }

            if (isset($filters['featured'])) {
                $query->featured();
            }

            // 排序
            $sortBy = $filters['sort_by'] ?? 'recommendation';
            switch ($sortBy) {
                case 'latest':
                    $query->orderBy('published_at', 'desc');
                    break;
                case 'popular':
                    $query->orderBy('view_count', 'desc');
                    break;
                case 'liked':
                    $query->orderBy('like_count', 'desc');
                    break;
                default:
                    $query->orderByRecommendation();
            }

            $works = $query->with(['user:id,username,avatar'])
                          ->paginate($filters['per_page'] ?? 20);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取成功',
                'data' => [
                    'works' => $works->items(),
                    'pagination' => [
                        'current_page' => $works->currentPage(),
                        'per_page' => $works->perPage(),
                        'total' => $works->total(),
                        'last_page' => $works->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters
            ];

            Log::error('获取作品展示库失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取失败',
                'data' => []
            ];
        }
    }
}
