<?php

namespace App\Services\Api;

use App\Helpers\LogCheckHelper;
use App\Models\Resource;
use App\Models\ResourceVersion;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Log;

/**
 * 🎯 作品发布权限检查服务
 * 严格按照dev-api-guidelines-add.mdc规范实现
 * 对应规范第13957行：作品发布权限检查系统
 */
class WorkPublishPermissionService
{
    /**
     * 🔧 LongDev1标记：检查模块内容是否允许发布 - 按规范第13967行
     * 
     * @param string $moduleType 模块类型
     * @param int $moduleId 模块ID
     * @param int $userId 用户ID
     * @return array 权限检查结果
     */
    public function checkPublishPermission(string $moduleType, int $moduleId, int $userId): array
    {
        try {
            // 🔧 LongDev1标记：查询该模块内容相关的资源审核状态 - 按规范第13971行
            $resources = Resource::where('module_type', $moduleType)
                ->where('module_id', $moduleId)
                ->where('user_id', $userId)
                ->with('currentVersion')
                ->get();

            if ($resources->isEmpty()) {
                return [
                    'allowed' => false,
                    'reason' => '未找到相关资源',
                    'code' => 'NO_RESOURCES_FOUND',
                    'details' => []
                ];
            }

            // 🔧 LongDev1标记：获取所有资源的审核状态 - 按规范第13986行
            $reviewStatuses = $resources->pluck('currentVersion.review_status')->filter()->unique();

            // 🔧 LongDev1标记：权限判断逻辑 - 按规范第13989行
            if ($reviewStatuses->contains('rejected') || $reviewStatuses->contains('flagged')) {
                return [
                    'allowed' => false,
                    'reason' => '包含被拒绝或标记的内容',
                    'code' => 'CONTENT_REJECTED',
                    'details' => $this->getReviewDetails($resources)
                ];
            }

            // 🔧 LongDev1标记：检查待审核状态 - 按规范第13998行
            if ($reviewStatuses->contains('not_reviewed') || $reviewStatuses->contains('manual_pending')) {
                return [
                    'allowed' => false,
                    'reason' => '内容正在审核中，请等待审核完成',
                    'code' => 'PENDING_REVIEW',
                    'details' => $this->getReviewDetails($resources)
                ];
            }

            // 🔧 LongDev1标记：检查通过审核状态 - 按规范第14007行
            if ($reviewStatuses->every(fn($status) => in_array($status, ['approved', 'auto_approved']))) {
                return [
                    'allowed' => true,
                    'reason' => '所有内容已通过审核',
                    'code' => 'APPROVED',
                    'resources' => $resources->count()
                ];
            }

            // 🔧 LongDev1标记：异常状态处理 - 按规范第14016行
            return [
                'allowed' => false,
                'reason' => '审核状态异常',
                'code' => 'UNKNOWN_STATUS'
            ];

        } catch (\Exception $e) {
            $error_context = [
                'module_type' => $moduleType,
                'module_id' => $moduleId,
                'user_id' => $userId,
            ];

            Log::error('发布权限检查失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'allowed' => false,
                'reason' => '发布权限检查失败',
                'code' => 'PERMISSION_CHECK_ERROR'
            ];
        }
    }

    /**
     * 🔧 LongDev1标记：获取审核详情 - 按规范第14024行
     * 
     * @param \Illuminate\Database\Eloquent\Collection $resources
     * @return array
     */
    private function getReviewDetails($resources): array
    {
        return $resources->map(function ($resource) {
            $currentVersion = $resource->currentVersion;
            return [
                'resource_uuid' => $resource->resource_uuid,
                'resource_id' => $resource->id,
                'review_status' => $currentVersion ? $currentVersion->review_status : 'no_version',
                'review_notes' => $currentVersion ? $currentVersion->review_notes : null,
                'reviewed_at' => $currentVersion ? $currentVersion->reviewed_at : null
            ];
        })->toArray();
    }

    /**
     * 🔧 LongDev1标记：检查单个资源的发布权限
     * 
     * @param int $resourceId 资源ID
     * @param int $userId 用户ID
     * @return array
     */
    public function checkResourcePublishPermission(int $resourceId, int $userId): array
    {
        try {
            $resource = Resource::where('id', $resourceId)
                ->where('user_id', $userId)
                ->with('currentVersion')
                ->first();

            if (!$resource) {
                return [
                    'allowed' => false,
                    'reason' => '资源不存在或无权限',
                    'code' => 'RESOURCE_NOT_FOUND'
                ];
            }

            $currentVersion = $resource->currentVersion;
            if (!$currentVersion) {
                return [
                    'allowed' => false,
                    'reason' => '资源版本不存在',
                    'code' => 'VERSION_NOT_FOUND'
                ];
            }

            $reviewStatus = $currentVersion->review_status;

            if (in_array($reviewStatus, ['rejected', 'flagged'])) {
                return [
                    'allowed' => false,
                    'reason' => '内容被拒绝或标记',
                    'code' => 'CONTENT_REJECTED',
                    'review_notes' => $currentVersion->review_notes
                ];
            }

            if (in_array($reviewStatus, ['not_reviewed', 'manual_pending'])) {
                return [
                    'allowed' => false,
                    'reason' => '内容正在审核中',
                    'code' => 'PENDING_REVIEW'
                ];
            }

            if (in_array($reviewStatus, ['approved', 'auto_approved'])) {
                return [
                    'allowed' => true,
                    'reason' => '内容已通过审核',
                    'code' => 'APPROVED',
                    'resource' => $resource
                ];
            }

            return [
                'allowed' => false,
                'reason' => '审核状态异常',
                'code' => 'UNKNOWN_STATUS'
            ];

        } catch (\Exception $e) {
            $error_context = [
                'resource_id' => $resourceId,
                'user_id' => $userId
            ];

            Log::error('资源发布权限检查失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'allowed' => false,
                'reason' => '权限检查失败',
                'code' => 'PERMISSION_CHECK_ERROR'
            ];
        }
    }

    /**
     * 🔧 LongDev1标记：获取模块类型到作品类型的映射 - 按规范第14110行
     *
     * @param string $moduleType
     * @return string
     */
    public function mapModuleTypeToWorkType(string $moduleType): string
    {
        try {
            $mapping = [
                'character' => 'character',
                'style' => 'style',
                'background_music' => 'background_music',
                'voice_tone' => 'voice_tone',
                'scene_sound' => 'scene_sound',
                'video' => 'composite_video',
                'story' => 'composite_video', // 故事最终生成视频
                'image' => 'composite_video'  // 图像可能用于视频合成
            ];

            return $mapping[$moduleType] ?? 'composite_video';

        } catch (\Exception $e) {
            $error_context = [
                'module_type' => $moduleType
            ];

            Log::error('模块类型映射失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 'composite_video'; // 默认返回值
        }
    }
}
