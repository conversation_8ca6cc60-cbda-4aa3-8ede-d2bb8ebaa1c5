<?php

namespace App\Services\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Follow;
use App\Models\Like;
use App\Models\Comment;
use App\Models\Share;
use App\Models\SocialActivity;
use App\Models\Notification;
use App\Models\Publication;
use App\Models\Template;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 社交互动服务
 * 第5阶段：社交功能扩展
 */
class SocialService
{
    /**
     * 管理关注
     */
    public function manageFollow(int $userId, array $followData): array
    {
        try {
            DB::beginTransaction();

            $targetUserId = $followData['target_user_id'];
            $action = $followData['action'];

            $existingFollow = Follow::where('follower_id', $userId)
                ->where('following_id', $targetUserId)
                ->first();

            if ($action === 'follow') {
                if ($existingFollow) {
                    return [
                        'code' => ApiCodeEnum::DUPLICATE_OPERATION,
                        'message' => '已经关注该用户',
                        'data' => []
                    ];
                }

                // 创建关注关系
                $follow = Follow::create([
                    'follower_id' => $userId,
                    'following_id' => $targetUserId,
                    'followed_at' => Carbon::now()
                ]);

                // 更新用户统计
                User::where('id', $targetUserId)->increment('follower_count');
                User::where('id', $userId)->increment('following_count');

                // 创建通知
                $this->createNotification($targetUserId, 'follow', '有新用户关注了你', [
                    'actor_id' => $userId,
                    'action' => 'follow'
                ]);

                // 记录社交活动
                $this->recordSocialActivity($userId, 'follow', 'user', $targetUserId);

                $isFollowing = true;
                $followedAt = $follow->followed_at->format('Y-m-d H:i:s');

            } else { // unfollow
                if (!$existingFollow) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '未关注该用户',
                        'data' => []
                    ];
                }

                $existingFollow->delete();

                // 更新用户统计
                User::where('id', $targetUserId)->decrement('follower_count');
                User::where('id', $userId)->decrement('following_count');

                $isFollowing = false;
                $followedAt = null;
            }

            // 获取最新统计
            $targetUser = User::find($targetUserId);
            $currentUser = User::find($userId);

            // 检查互相关注
            $mutualFollow = Follow::where('follower_id', $targetUserId)
                ->where('following_id', $userId)
                ->exists();

            DB::commit();

            Log::info('关注操作成功', [
                'user_id' => $userId,
                'target_user_id' => $targetUserId,
                'action' => $action
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '关注操作成功',
                'data' => [
                    'target_user_id' => $targetUserId,
                    'action' => $action,
                    'is_following' => $isFollowing,
                    'follower_count' => $targetUser->follower_count,
                    'following_count' => $currentUser->following_count,
                    'mutual_follow' => $mutualFollow,
                    'followed_at' => $followedAt
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'target_user_id' => $followData['target_user_id'],
                'action' => $followData['action'],
            ];

            Log::error('关注操作失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '关注操作失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取关注列表
     */
    public function getFollowList(int $currentUserId, int $targetUserId, array $params): array
    {
        try {
            $type = $params['type'];
            $perPage = $params['per_page'];
            $page = $params['page'];

            switch ($type) {
                case 'following':
                    $query = Follow::where('follower_id', $targetUserId)
                        ->with(['following:id,username,avatar,bio,level,follower_count,following_count']);
                    break;
                case 'followers':
                    $query = Follow::where('following_id', $targetUserId)
                        ->with(['follower:id,username,avatar,bio,level,follower_count,following_count']);
                    break;
                case 'mutual':
                    $followingIds = Follow::where('follower_id', $targetUserId)->pluck('following_id');
                    $query = Follow::where('following_id', $targetUserId)
                        ->whereIn('follower_id', $followingIds)
                        ->with(['follower:id,username,avatar,bio,level,follower_count,following_count']);
                    break;
            }

            $follows = $query->paginate($perPage, ['*'], 'page', $page);

            $userList = [];
            foreach ($follows->items() as $follow) {
                $user = $type === 'following' ? $follow->following : $follow->follower;
                
                if (!$user) continue;

                // 检查当前用户与此用户的关系
                $isFollowing = Follow::where('follower_id', $currentUserId)
                    ->where('following_id', $user->id)
                    ->exists();
                
                $isFollowedBy = Follow::where('follower_id', $user->id)
                    ->where('following_id', $currentUserId)
                    ->exists();

                $userList[] = [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'avatar' => $user->avatar,
                    'bio' => $user->bio,
                    'level' => $user->level ?? 1,
                    'follower_count' => $user->follower_count ?? 0,
                    'following_count' => $user->following_count ?? 0,
                    'is_following' => $isFollowing,
                    'is_followed_by' => $isFollowedBy,
                    'mutual_follow' => $isFollowing && $isFollowedBy,
                    'followed_at' => $follow->followed_at->format('Y-m-d H:i:s'),
                    'recent_activity' => $this->getUserRecentActivity($user->id)
                ];
            }

            // 获取统计信息
            $statistics = $this->getFollowStatistics($targetUserId);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'list_type' => $type,
                    'user_id' => $targetUserId,
                    'users' => $userList,
                    'statistics' => $statistics,
                    'pagination' => [
                        'current_page' => $follows->currentPage(),
                        'per_page' => $follows->perPage(),
                        'total' => $follows->total(),
                        'last_page' => $follows->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'current_user_id' => $currentUserId,
                'target_user_id' => $targetUserId,
                'params' => $params,
            ];

            Log::error('获取关注列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取关注列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 管理点赞
     */
    public function manageLike(int $userId, array $likeData): array
    {
        try {
            DB::beginTransaction();

            $targetType = $likeData['target_type'];
            $targetId = $likeData['target_id'];
            $action = $likeData['action'];

            // 验证目标是否存在
            $target = $this->getTargetModel($targetType, $targetId);
            if (!$target) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '目标内容不存在',
                    'data' => []
                ];
            }

            $existingLike = Like::where('user_id', $userId)
                ->where('target_type', $targetType)
                ->where('target_id', $targetId)
                ->first();

            if ($action === 'like') {
                if ($existingLike) {
                    return [
                        'code' => ApiCodeEnum::DUPLICATE_OPERATION,
                        'message' => '已经点赞过了',
                        'data' => []
                    ];
                }

                // 创建点赞记录
                $like = Like::create([
                    'user_id' => $userId,
                    'target_type' => $targetType,
                    'target_id' => $targetId,
                    'liked_at' => Carbon::now()
                ]);

                // 更新目标的点赞数
                $target->increment('like_count');

                // 创建通知（如果不是自己的内容）
                if ($target->user_id && $target->user_id !== $userId) {
                    $this->createNotification($target->user_id, 'like', '有人点赞了你的作品', [
                        'actor_id' => $userId,
                        'target_type' => $targetType,
                        'target_id' => $targetId
                    ]);
                }

                // 记录社交活动
                $this->recordSocialActivity($userId, 'like', $targetType, $targetId);

                $isLiked = true;
                $likedAt = $like->liked_at->format('Y-m-d H:i:s');

            } else { // unlike
                if (!$existingLike) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '未点赞过该内容',
                        'data' => []
                    ];
                }

                $existingLike->delete();
                $target->decrement('like_count');

                $isLiked = false;
                $likedAt = null;
            }

            DB::commit();

            Log::info('点赞操作成功', [
                'user_id' => $userId,
                'target_type' => $targetType,
                'target_id' => $targetId,
                'action' => $action
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '点赞操作成功',
                'data' => [
                    'target_type' => $targetType,
                    'target_id' => $targetId,
                    'action' => $action,
                    'is_liked' => $isLiked,
                    'like_count' => $target->like_count,
                    'liked_at' => $likedAt
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'like_data' => $likeData,
            ];

            Log::error('点赞操作失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '点赞操作失败',
                'data' => null
            ];
        }
    }

    /**
     * 创建评论
     */
    public function createComment(int $userId, array $commentData): array
    {
        try {
            DB::beginTransaction();

            $targetType = $commentData['target_type'];
            $targetId = $commentData['target_id'];
            $content = $commentData['content'];
            $parentId = $commentData['parent_id'] ?? null;

            // 验证目标是否存在
            $target = $this->getTargetModel($targetType, $targetId);
            if (!$target) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '目标内容不存在',
                    'data' => []
                ];
            }

            // 验证父评论（如果是回复）
            if ($parentId) {
                $parentComment = Comment::find($parentId);
                if (!$parentComment || $parentComment->target_id !== $targetId) {
                    return [
                        'code' => ApiCodeEnum::INVALID_PARAMETER,
                        'message' => '父评论不存在或不匹配',
                        'data' => []
                    ];
                }
            }

            // 创建评论
            $comment = Comment::create([
                'user_id' => $userId,
                'target_type' => $targetType,
                'target_id' => $targetId,
                'parent_id' => $parentId,
                'content' => $content,
                'like_count' => 0,
                'reply_count' => 0
            ]);

            // 更新目标的评论数
            $target->increment('comment_count');

            // 如果是回复，更新父评论的回复数
            if ($parentId) {
                Comment::where('id', $parentId)->increment('reply_count');
            }

            // 创建通知
            if ($target->user_id && $target->user_id !== $userId) {
                $this->createNotification($target->user_id, 'comment', '有人评论了你的作品', [
                    'actor_id' => $userId,
                    'target_type' => $targetType,
                    'target_id' => $targetId,
                    'comment_id' => $comment->id
                ]);
            }

            // 如果是回复，通知被回复的用户
            if ($parentId && $parentComment->user_id !== $userId) {
                $this->createNotification($parentComment->user_id, 'reply', '有人回复了你的评论', [
                    'actor_id' => $userId,
                    'comment_id' => $comment->id,
                    'parent_comment_id' => $parentId
                ]);
            }

            // 记录社交活动
            $this->recordSocialActivity($userId, 'comment', $targetType, $targetId);

            DB::commit();

            // 获取用户信息
            $user = User::find($userId);

            Log::info('评论创建成功', [
                'user_id' => $userId,
                'target_type' => $targetType,
                'target_id' => $targetId,
                'comment_id' => $comment->id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '评论发布成功',
                'data' => [
                    'comment_id' => $comment->id,
                    'target_type' => $targetType,
                    'target_id' => $targetId,
                    'content' => $comment->content,
                    'parent_id' => $comment->parent_id,
                    'author' => [
                        'user_id' => $user->id,
                        'username' => $user->username,
                        'avatar' => $user->avatar
                    ],
                    'like_count' => 0,
                    'reply_count' => 0,
                    'is_liked' => false,
                    'created_at' => $comment->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'comment_data' => $commentData,
            ];

            Log::error('创建评论失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '评论发布失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取评论列表
     */
    public function getComments(?int $userId, array $params): array
    {
        try {
            $targetType = $params['target_type'];
            $targetId = $params['target_id'];
            $sort = $params['sort'];
            $perPage = $params['per_page'];
            $page = $params['page'];

            // 验证目标是否存在
            $target = $this->getTargetModel($targetType, $targetId);
            if (!$target) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '目标内容不存在',
                    'data' => []
                ];
            }

            // 构建查询
            $query = Comment::where('target_type', $targetType)
                ->where('target_id', $targetId)
                ->whereNull('parent_id') // 只获取顶级评论
                ->with([
                    'user:id,username,avatar,level',
                    'replies' => function($q) {
                        $q->with('user:id,username,avatar,level')
                          ->orderBy('created_at', 'asc')
                          ->limit(3); // 只显示前3个回复
                    }
                ]);

            // 排序
            switch ($sort) {
                case 'latest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'popular':
                    $query->orderBy('like_count', 'desc')
                          ->orderBy('created_at', 'desc');
                    break;
            }

            $comments = $query->paginate($perPage, ['*'], 'page', $page);

            $commentList = [];
            foreach ($comments->items() as $comment) {
                // 检查当前用户是否点赞了这条评论
                $isLiked = false;
                if ($userId) {
                    $isLiked = Like::where('user_id', $userId)
                        ->where('target_type', 'comment')
                        ->where('target_id', $comment->id)
                        ->exists();
                }

                // 处理回复列表
                $replies = [];
                foreach ($comment->replies as $reply) {
                    $isReplyLiked = false;
                    if ($userId) {
                        $isReplyLiked = Like::where('user_id', $userId)
                            ->where('target_type', 'comment')
                            ->where('target_id', $reply->id)
                            ->exists();
                    }

                    $replies[] = [
                        'comment_id' => $reply->id,
                        'content' => $reply->content,
                        'author' => [
                            'user_id' => $reply->user->id,
                            'username' => $reply->user->username,
                            'avatar' => $reply->user->avatar,
                            'level' => $reply->user->level ?? 1
                        ],
                        'like_count' => $reply->like_count,
                        'is_liked' => $isReplyLiked,
                        'created_at' => $reply->created_at->format('Y-m-d H:i:s')
                    ];
                }

                $commentList[] = [
                    'comment_id' => $comment->id,
                    'content' => $comment->content,
                    'author' => [
                        'user_id' => $comment->user->id,
                        'username' => $comment->user->username,
                        'avatar' => $comment->user->avatar,
                        'level' => $comment->user->level ?? 1
                    ],
                    'like_count' => $comment->like_count,
                    'reply_count' => $comment->reply_count,
                    'is_liked' => $isLiked,
                    'replies' => $replies,
                    'has_more_replies' => $comment->reply_count > 3,
                    'created_at' => $comment->created_at->format('Y-m-d H:i:s')
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'target_type' => $targetType,
                    'target_id' => $targetId,
                    'comments' => $commentList,
                    'pagination' => [
                        'current_page' => $comments->currentPage(),
                        'per_page' => $comments->perPage(),
                        'total' => $comments->total(),
                        'last_page' => $comments->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'params' => $params,
            ];

            Log::error('获取评论列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取评论列表失败',
                'data' => null
            ];
        }
    }

    // 私有辅助方法
    private function getTargetModel(string $targetType, int $targetId)
    {
        switch ($targetType) {
            case 'publication':
                return Publication::find($targetId);
            case 'template':
                return Template::find($targetId);
            case 'comment':
                return Comment::find($targetId);
            default:
                return null;
        }
    }

    private function createNotification(int $userId, string $type, string $message, array $data): void
    {
        Notification::create([
            'user_id' => $userId,
            'type' => $type,
            'title' => $this->getNotificationTitle($type),
            'message' => $message,
            'data' => $data,
            'is_read' => false
        ]);
    }

    private function getNotificationTitle(string $type): string
    {
        $titles = [
            'follow' => '新关注',
            'like' => '点赞通知',
            'comment' => '评论通知',
            'reply' => '回复通知',
            'share' => '分享通知'
        ];

        return $titles[$type] ?? '通知';
    }

    private function recordSocialActivity(int $userId, string $action, string $targetType, int $targetId): void
    {
        SocialActivity::create([
            'user_id' => $userId,
            'action' => $action,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'metadata' => [
                'timestamp' => Carbon::now()->timestamp,
                'ip' => request()->ip()
            ]
        ]);
    }

    private function getUserRecentActivity(int $userId): string
    {
        $activity = SocialActivity::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$activity) {
            return '暂无活动';
        }

        $timeAgo = $activity->created_at->diffForHumans();
        $actionMap = [
            'like' => '点赞了作品',
            'comment' => '发表了评论',
            'follow' => '关注了用户',
            'share' => '分享了内容'
        ];

        $actionText = $actionMap[$activity->action] ?? '进行了活动';
        return $timeAgo . $actionText;
    }

    private function getFollowStatistics(int $userId): array
    {
        $followingCount = Follow::where('follower_id', $userId)->count();
        $followerCount = Follow::where('following_id', $userId)->count();
        
        // 互相关注数
        $followingIds = Follow::where('follower_id', $userId)->pluck('following_id');
        $mutualCount = Follow::where('following_id', $userId)
            ->whereIn('follower_id', $followingIds)
            ->count();

        // 今日新增粉丝
        $newFollowersToday = Follow::where('following_id', $userId)
            ->whereDate('followed_at', Carbon::today())
            ->count();

        return [
            'total_following' => $followingCount,
            'total_followers' => $followerCount,
            'mutual_follows' => $mutualCount,
            'new_followers_today' => $newFollowersToday
        ];
    }

    /**
     * 分享内容
     */
    public function shareContent(int $userId, array $shareData): array
    {
        try {
            DB::beginTransaction();

            $targetType = $shareData['target_type'];
            $targetId = $shareData['target_id'];
            $platform = $shareData['platform'];
            $message = $shareData['message'] ?? '';

            // 验证目标是否存在
            $target = $this->getTargetModel($targetType, $targetId);
            if (!$target) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '分享目标不存在',
                    'data' => []
                ];
            }

            // 创建分享记录
            $share = Share::create([
                'user_id' => $userId,
                'target_type' => $targetType,
                'target_id' => $targetId,
                'platform' => $platform,
                'message' => $message,
                'share_url' => $this->generateShareUrl($targetType, $targetId),
                'shared_at' => Carbon::now()
            ]);

            // 更新分享统计
            if ($targetType === 'publication') {
                Publication::where('id', $targetId)->increment('share_count');
            } elseif ($targetType === 'template') {
                Template::where('id', $targetId)->increment('share_count');
            }

            // 记录社交活动
            $this->recordSocialActivity($userId, 'share', $targetType, $targetId);

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '分享成功',
                'data' => [
                    'share_id' => $share->id,
                    'target_type' => $targetType,
                    'target_id' => $targetId,
                    'platform' => $platform,
                    'share_url' => $share->share_url,
                    'share_count' => $target->share_count ?? 0,
                    'shared_at' => $share->shared_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'share_data' => $shareData,
            ];

            Log::error('分享内容失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '分享失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取社交动态
     */
    public function getSocialFeed(int $userId, array $params): array
    {
        try {
            $type = $params['type'];
            $perPage = $params['per_page'];
            $page = $params['page'];

            // 获取关注的用户ID列表
            $followingIds = Follow::where('follower_id', $userId)->pluck('following_id')->toArray();
            $followingIds[] = $userId; // 包含自己的动态

            $query = SocialActivity::whereIn('user_id', $followingIds)
                ->with(['user:id,username,avatar']);

            // 根据类型过滤
            if ($type !== 'all') {
                switch ($type) {
                    case 'publications':
                        $query->where('action', 'create')
                              ->where('target_type', 'publication');
                        break;
                    case 'follows':
                        $query->where('action', 'follow');
                        break;
                    case 'likes':
                        $query->where('action', 'like');
                        break;
                    case 'comments':
                        $query->where('action', 'comment');
                        break;
                }
            }

            $activities = $query->orderBy('created_at', 'desc')
                               ->paginate($perPage, ['*'], 'page', $page);

            $feedList = [];
            foreach ($activities->items() as $activity) {
                $target = $this->getTargetModel($activity->target_type, $activity->target_id);
                
                $feedList[] = [
                    'activity_id' => $activity->id,
                    'type' => $activity->action . '_' . $activity->target_type,
                    'actor' => [
                        'user_id' => $activity->user->id,
                        'username' => $activity->user->username,
                        'avatar' => $activity->user->avatar
                    ],
                    'action' => $this->getActionText($activity->action),
                    'target' => [
                        'type' => $activity->target_type,
                        'id' => $activity->target_id,
                        'title' => $target->title ?? $target->name ?? '未知',
                        'thumbnail' => $target->thumbnail ?? $target->cover ?? null
                    ],
                    'metadata' => [
                        'like_count' => $target->like_count ?? 0,
                        'comment_count' => $target->comment_count ?? 0
                    ],
                    'created_at' => $activity->created_at->format('Y-m-d H:i:s')
                ];
            }

            // 获取未读数量
            $unreadCount = Notification::where('user_id', $userId)
                                     ->where('is_read', false)
                                     ->count();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'feed_type' => $type,
                    'activities' => $feedList,
                    'unread_count' => $unreadCount,
                    'pagination' => [
                        'current_page' => $activities->currentPage(),
                        'per_page' => $activities->perPage(),
                        'total' => $activities->total(),
                        'last_page' => $activities->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'params' => $params,
            ];

            Log::error('获取社交动态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取社交动态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取通知
     */
    public function getNotifications(int $userId, array $params): array
    {
        try {
            $type = $params['type'];
            $status = $params['status'];
            $perPage = $params['per_page'];
            $page = $params['page'];

            $query = Notification::where('user_id', $userId)
                                ->with(['actor:id,username,avatar']);

            // 根据类型过滤
            if ($type !== 'all') {
                $query->where('type', $type);
            }

            // 根据状态过滤
            if ($status !== 'all') {
                $isRead = $status === 'read';
                $query->where('is_read', $isRead);
            }

            $notifications = $query->orderBy('created_at', 'desc')
                                  ->paginate($perPage, ['*'], 'page', $page);

            $notificationList = [];
            foreach ($notifications->items() as $notification) {
                $data = json_decode($notification->data, true) ?? [];
                $target = null;
                
                if (isset($data['target_type']) && isset($data['target_id'])) {
                    $target = $this->getTargetModel($data['target_type'], $data['target_id']);
                }

                $notificationList[] = [
                    'notification_id' => $notification->id,
                    'type' => $notification->type,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'actor' => [
                        'user_id' => $data['actor_id'] ?? null,
                        'username' => $notification->actor->username ?? '系统',
                        'avatar' => $notification->actor->avatar ?? null
                    ],
                    'target' => $target ? [
                        'type' => $data['target_type'],
                        'id' => $data['target_id'],
                        'title' => $target->title ?? $target->name ?? '未知'
                    ] : null,
                    'is_read' => $notification->is_read,
                    'created_at' => $notification->created_at->format('Y-m-d H:i:s')
                ];
            }

            // 获取未读数量
            $unreadCount = Notification::where('user_id', $userId)
                                     ->where('is_read', false)
                                     ->count();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'notifications' => $notificationList,
                    'unread_count' => $unreadCount,
                    'pagination' => [
                        'current_page' => $notifications->currentPage(),
                        'per_page' => $notifications->perPage(),
                        'total' => $notifications->total(),
                        'last_page' => $notifications->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'params' => $params,
            ];

            Log::error('获取通知失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取通知失败',
                'data' => null
            ];
        }
    }

    /**
     * 标记通知已读
     */
    public function markNotificationsRead(int $userId, array $notificationIds = []): array
    {
        try {
            DB::beginTransaction();

            $query = Notification::where('user_id', $userId)
                                ->where('is_read', false);

            if (!empty($notificationIds)) {
                $query->whereIn('id', $notificationIds);
            }

            $markedCount = $query->update([
                'is_read' => true,
                'read_at' => Carbon::now()
            ]);

            // 获取剩余未读数量
            $remainingUnread = Notification::where('user_id', $userId)
                                         ->where('is_read', false)
                                         ->count();

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知标记成功',
                'data' => [
                    'marked_count' => $markedCount,
                    'remaining_unread' => $remainingUnread,
                    'marked_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'notification_ids' => $notificationIds,
            ];

            Log::error('标记通知已读失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '标记通知失败',
                'data' => null
            ];
        }
    }

    /**
     * 生成分享链接
     */
    private function generateShareUrl(string $targetType, int $targetId): string
    {
        $baseUrl = config('app.url');
        return "{$baseUrl}/share/{$targetType}/{$targetId}";
    }

    /**
     * 获取动作文本
     */
    private function getActionText(string $action): string
    {
        $actionMap = [
            'create' => '发布了新作品',
            'like' => '点赞了',
            'comment' => '评论了',
            'follow' => '关注了用户',
            'share' => '分享了'
        ];

        return $actionMap[$action] ?? '进行了操作';
    }
}
